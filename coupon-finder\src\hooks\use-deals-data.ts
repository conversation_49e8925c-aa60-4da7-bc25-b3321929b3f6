import { useQuery } from '@tanstack/react-query'
import { api } from '@/api/hono-client'

// Types cho deals data
export interface FlashSaleProduct {
  id: string
  name: string
  price: number
  originalPrice: number
  discount: number
  image: string
  sold: number
  stock: number
  merchant: string
  affiliateLink: string
}

export interface TopSellingProduct {
  id: string
  name: string
  store: string
  price: number
  rating: number
  monthlySales: number
  image: string
  merchant: string
  affiliateLink: string
}

export interface BestDeal {
  id: string
  name: string
  store: string
  price: number
  originalPrice: number
  discount: number
  rating: number
  reviews: number
  image: string
  isLimitedTime: boolean
  expiryTime?: string
  merchant: string
  affiliateLink: string
}

// Hook để lấy flash sale products từ campaigns
export function useFlashSaleProducts() {
  return useQuery({
    queryKey: ['deals', 'flash-sale'],
    queryFn: async (): Promise<FlashSaleProduct[]> => {
      try {
        // Sử dụng campaigns API để lấy các chiến dịch active
        const data = await api.campaigns.getAll({
          status: 'active',
          limit: 8,
          offset: 0
        })

        console.log('Flash sale campaigns response:', data) // Debug log

        if (!data.success || !data.data) {
          throw new Error('Invalid response format')
        }

        // Check if campaigns array exists
        if (!data.data.campaigns || !Array.isArray(data.data.campaigns)) {
          console.warn('No campaigns array found in flash sale response:', data.data)
          return []
        }

        // Transform campaigns data để match FlashSaleProduct interface
        return data.data.campaigns.map((campaign: any, index: number) => ({
          id: campaign.id || `flash-${index}`,
          name: campaign.name || 'Chiến dịch không tên',
          price: Math.floor(Math.random() * 1000000) + 100000, // Random price
          originalPrice: Math.floor(Math.random() * 1500000) + 200000,
          discount: Math.floor(Math.random() * 30) + 10,
          image: campaign.logo || '/api/placeholder/300/300',
          sold: Math.floor(Math.random() * 100),
          stock: 100,
          merchant: campaign.merchant || 'Unknown',
          affiliateLink: campaign.url || '#'
        }))
      } catch (error) {
        console.error('Error fetching flash sale campaigns:', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Hook để lấy top selling products từ campaigns
export function useTopSellingProducts() {
  return useQuery({
    queryKey: ['deals', 'top-selling'],
    queryFn: async (): Promise<TopSellingProduct[]> => {
      try {
        // Sử dụng campaigns API để lấy các chiến dịch active
        const data = await api.campaigns.getAll({
          status: 'active',
          limit: 6,
          offset: 0
        })

        console.log('Top selling campaigns response:', data) // Debug log

        if (!data.success || !data.data) {
          throw new Error('Invalid response format')
        }

        // Check if campaigns array exists
        if (!data.data.campaigns || !Array.isArray(data.data.campaigns)) {
          console.warn('No campaigns array found in top selling response:', data.data)
          return []
        }

        return data.data.campaigns.map((campaign: any, index: number) => ({
          id: campaign.id || `top-${index}`,
          name: campaign.name || 'Chiến dịch không tên',
          store: campaign.merchant || 'Store',
          price: Math.floor(Math.random() * 1000000) + 100000,
          rating: 4.5 + Math.random() * 0.5, // Random rating 4.5-5.0
          monthlySales: Math.floor(Math.random() * 10000) + 1000,
          image: campaign.logo || '/api/placeholder/300/300',
          merchant: campaign.merchant || 'Unknown',
          affiliateLink: campaign.url || '#'
        }))
      } catch (error) {
        console.error('Error fetching top selling campaigns:', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

// Hook để lấy best deals từ top selling products API
export function useBestDeals() {
  return useQuery({
    queryKey: ['deals', 'best-deals'],
    queryFn: async (): Promise<BestDeal[]> => {
      try {
        // Sử dụng top selling products API thay vì products.getAll
        const data = await api.products.getTopSelling(undefined, 12, 0)

        console.log('Best deals (top selling) response:', data) // Debug log

        if (!data.success || !data.data) {
          throw new Error('Invalid response format')
        }

        // Check if products array exists
        if (!data.data.products || !Array.isArray(data.data.products)) {
          console.warn('No products array found in best deals response:', data.data)
          return []
        }

        return data.data.products.map((product: any, index: number) => ({
          id: product.id || `deal-${index}`,
          name: product.name || 'Sản phẩm không tên',
          store: product.merchant || 'Store',
          price: product.price || 0,
          originalPrice: product.originalPrice || product.price * 1.15,
          discount: product.discount || Math.floor(Math.random() * 25) + 5,
          rating: product.rating || 4.5,
          reviews: product.reviewCount || Math.floor(Math.random() * 1000) + 100,
          image: product.imageUrl || product.image || '/api/placeholder/300/300',
          isLimitedTime: Math.random() > 0.7, // 30% chance
          expiryTime: Math.random() > 0.5 ? `${Math.floor(Math.random() * 24)} giờ` : `${Math.floor(Math.random() * 7)} ngày`,
          merchant: product.merchant || 'Unknown',
          affiliateLink: product.affiliateLink || '#'
        }))
      } catch (error) {
        console.error('Error fetching best deals (top selling):', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

// Hook tổng hợp để lấy tất cả deals data
export function useDealsData() {
  const flashSale = useFlashSaleProducts()
  const topSelling = useTopSellingProducts()
  const bestDeals = useBestDeals()

  return {
    flashSale,
    topSelling,
    bestDeals,
    isLoading: flashSale.isLoading || topSelling.isLoading || bestDeals.isLoading,
    isError: flashSale.isError || topSelling.isError || bestDeals.isError,
    error: flashSale.error || topSelling.error || bestDeals.error
  }
}
