/**
 * AccessTrade API Client
 * Tích hợp với AccessTrade Vietnam API để lấy dữ liệu affiliate marketing
 */

import { z } from 'zod'

// Base configuration
const ACCESSTRADE_BASE_URL = 'https://api.accesstrade.vn'
const API_VERSION = 'v1'

// Zod schemas cho AccessTrade API responses
export const AccessTradeOfferSchema = z.object({
  id: z.string(),
  name: z.string(),
  content: z.string(),
  image: z.string().url(),
  link: z.string().url(),
  aff_link: z.string().url(),
  merchant: z.string(),
  domain: z.string(),
  start_time: z.string(),
  end_time: z.string().nullable(),
  categories: z.array(z.object({
    category_name: z.string(),
    category_name_show: z.string(),
    category_no: z.union([z.number(), z.string()]), // Có thể là string hoặc number
  })),
  banners: z.array(z.object({
    link: z.string().url(),
    width: z.number(),
    height: z.number(),
  })).optional().default([]), // Banners có thể rỗng
  coupons: z.array(z.any()).optional().default([]), // Field coupons từ API
})

export const AccessTradeCampaignSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.union([z.string(), z.object({}).passthrough()]).transform(val =>
    typeof val === 'string' ? val : JSON.stringify(val)
  ),
  merchant: z.string(),
  category: z.string(),
  sub_category: z.string(),
  status: z.number(),
  approval: z.enum(['unregistered', 'pending', 'successful']),
  start_time: z.string(),
  end_time: z.string().nullable(),
  url: z.string().url(),
  cookie_duration: z.number(),
  conversion_policy: z.string().optional(),
  cookie_policy: z.string(),
  scope: z.string(),
  type: z.number(),
  logo: z.string().optional(),
  max_com: z.string().optional(),
})

export const AccessTradeProductSchema = z.object({
  product_id: z.string(),
  name: z.string(),
  desc: z.string().nullable().optional(),
  short_desc: z.string().nullable().optional(),
  image: z.string().url(),
  link: z.string().url().optional(),
  aff_link: z.string().url(),
  price: z.union([z.string(), z.number()]).transform(val => String(val)),
  discount: z.union([z.string(), z.number()]).transform(val => String(val)),
  brand: z.string().optional(),
  category_id: z.string().optional(),
  category_name: z.string().optional(),
  campaign: z.string().optional(),
  domain: z.string(),
  sku: z.string().nullable().optional(),
  status_discount: z.number().optional(),
  discount_amount: z.number(),
  discount_rate: z.number(),
  update_time: z.string().optional(),
  url: z.string().url().optional(),
  cate: z.string().optional(),
  merchant: z.string().optional(),
  shop_id: z.string().optional(),
  shop_name: z.string().optional(),
  promotion: z.string().nullable().optional(),
  category_commission: z.object({
    partner_reward: z.string(),
    reward_type: z.string(),
  }).optional(),
})

export const AccessTradeTopProductSchema = z.object({
  product_id: z.string(),
  name: z.string(),
  desc: z.string().nullable(),
  image: z.string().url(),
  link: z.string().url(),
  aff_link: z.string().url(),
  price: z.string(),
  discount: z.string(),
  brand: z.string(),
  category_id: z.string(),
  category_name: z.string(),
  product_category: z.string(),
})

// Type exports
export type AccessTradeOffer = z.infer<typeof AccessTradeOfferSchema>
export type AccessTradeCampaign = z.infer<typeof AccessTradeCampaignSchema>
export type AccessTradeProduct = z.infer<typeof AccessTradeProductSchema>
export type AccessTradeTopProduct = z.infer<typeof AccessTradeTopProductSchema>

// API Response types
export interface AccessTradeOffersResponse {
  data: AccessTradeOffer[]
  total?: number
}

export interface AccessTradeCampaignsResponse {
  data: AccessTradeCampaign[]
  total: number
}

export interface AccessTradeProductsResponse {
  data: AccessTradeProduct[]
  total?: number
}

export interface AccessTradeTopProductsResponse {
  data: AccessTradeTopProduct[]
  total: number
}

// Error types
export class AccessTradeError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message)
    this.name = 'AccessTradeError'
  }
}

// AccessTrade API Client class
export class AccessTradeClient {
  private apiKey: string
  private baseUrl: string

  constructor(apiKey: string) {
    this.apiKey = apiKey
    this.baseUrl = `${ACCESSTRADE_BASE_URL}/${API_VERSION}`
  }

  /**
   * Make authenticated request to AccessTrade API
   */
  async makeRequest<T>(
    endpoint: string,
    params: Record<string, string | number | undefined> = {}
  ): Promise<T> {
    // Filter out undefined values
    const cleanParams = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .reduce((acc, [key, value]) => {
        acc[key] = String(value)
        return acc
      }, {} as Record<string, string>)

    const url = new URL(`${this.baseUrl}${endpoint}`)
    Object.entries(cleanParams).forEach(([key, value]) => {
      url.searchParams.append(key, value)
    })

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Authorization': `Token ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new AccessTradeError(
        `AccessTrade API error: ${errorText}`,
        response.status,
        response.status.toString()
      )
    }

    return response.json()
  }

  /**
   * Lấy danh sách offers/coupons từ AccessTrade
   */
  async getCoupons(params: {
    scope?: 'expiring' | 'all'
    merchant?: string
    categories?: string[]
    domain?: string
    coupon?: 0 | 1
    status?: 0 | 1
  } = {}): Promise<AccessTradeOffersResponse> {
    const queryParams = {
      scope: params.scope,
      merchant: params.merchant,
      categories: params.categories?.join(','),
      domain: params.domain,
      coupon: params.coupon,
      status: params.status,
    }

    const response = await this.makeRequest<any>('/offers_informations', queryParams)

    // Validate response structure - AccessTrade API trả về { data: [...] }
    if (!response || !response.data || !Array.isArray(response.data)) {
      throw new AccessTradeError('Invalid response format from AccessTrade offers API')
    }

    return {
      data: response.data.map((offer: any) => AccessTradeOfferSchema.parse(offer)),
      total: response.data.length,
    }
  }



  /**
   * Tìm kiếm sản phẩm từ datafeeds
   */
  async searchProducts(params: {
    campaign?: string
    domain?: string
    discount_amount_from?: number
    discount_amount_to?: number
    discount_rate_from?: number
    discount_rate_to?: number
    page?: number
    limit?: number
    price_from?: number
    price_to?: number
    discount_from?: number
    discount_to?: number
    status_discount?: 0 | 1
    update_from?: string
    update_to?: string
  } = {}): Promise<AccessTradeProductsResponse> {
    const queryParams = {
      campaign: params.campaign,
      domain: params.domain,
      discount_amount_from: params.discount_amount_from,
      discount_amount_to: params.discount_amount_to,
      discount_rate_from: params.discount_rate_from,
      discount_rate_to: params.discount_rate_to,
      page: params.page || 1,
      limit: Math.min(params.limit || 50, 200), // Max 200 theo API docs
      price_from: params.price_from,
      price_to: params.price_to,
      discount_from: params.discount_from,
      discount_to: params.discount_to,
      status_discount: params.status_discount,
      update_from: params.update_from,
      update_to: params.update_to,
    }

    const response = await this.makeRequest<any>('/datafeeds', queryParams)
    
    if (!response.data || !Array.isArray(response.data)) {
      throw new AccessTradeError('Invalid response format from AccessTrade datafeeds API')
    }

    return {
      data: response.data.map((product: any) => AccessTradeProductSchema.parse(product)),
      total: response.total,
    }
  }

  /**
   * Lấy top sản phẩm bán chạy
   */
  async getTopSellingProducts(params: {
    date_from?: string
    date_to?: string
    merchant?: string
  } = {}): Promise<AccessTradeTopProductsResponse> {
    const queryParams = {
      date_from: params.date_from,
      date_to: params.date_to,
      merchant: params.merchant,
    }

    const response = await this.makeRequest<any>('/top_products', queryParams)
    
    if (!response.data || !Array.isArray(response.data)) {
      throw new AccessTradeError('Invalid response format from AccessTrade top products API')
    }

    return {
      data: response.data.map((product: any) => AccessTradeTopProductSchema.parse(product)),
      total: response.total || response.data.length,
    }
  }

  /**
   * Tạo tracking link từ URL gốc (POST request)
   */
  async createTrackingLink(params: {
    campaign_id: string
    urls: string[]
    utm_source?: string
    utm_medium?: string
    utm_campaign?: string
    utm_content?: string
    sub1?: string
    sub2?: string
    sub3?: string
    sub4?: string
  }): Promise<{
    success_link: Array<{
      aff_link: string
      first_link: string | null
      short_link: string
      url_origin: string
    }>
    error_link: any[]
    suspend_url: any[]
  }> {
    const url = `${this.baseUrl}/product_link/create`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new AccessTradeError(
        `AccessTrade API error: ${errorText}`,
        response.status,
        response.status.toString()
      )
    }

    const result = await response.json() as any
    return result.data
  }

  /**
   * Tìm kiếm coupons theo URL sản phẩm
   */
  async searchCouponsByUrl(url: string): Promise<{
    count: number
    data: AccessTradeOffer[]
  }> {
    const queryParams = {
      URL: url,
    }

    const response = await this.makeRequest<any>('/offers_informations/coupon', queryParams)

    if (!response.data || !response.data.data || !Array.isArray(response.data.data)) {
      return { count: 0, data: [] }
    }

    return {
      count: response.data.count || 0,
      data: response.data.data.map((offer: any) => AccessTradeOfferSchema.parse(offer)),
    }
  }

  /**
   * Lấy danh sách coupons hot/ưu tiên
   */
  async getHotCoupons(params: {
    limit?: number
    date?: 1 | 2 // 1: theo tuần, 2: theo tháng
  } = {}): Promise<{
    count: number
    data: AccessTradeOffer[]
  }> {
    const queryParams = {
      limit: params.limit || 20,
      date: params.date || 1,
    }

    const response = await this.makeRequest<any>('/offers_informations/coupon_hot', queryParams)

    if (!response.data || !response.data.data || !Array.isArray(response.data.data)) {
      return { count: 0, data: [] }
    }

    return {
      count: response.data.count || 0,
      data: response.data.data.map((offer: any) => AccessTradeOfferSchema.parse(offer)),
    }
  }

  /**
   * Tạo affiliate link từ URL gốc (legacy method)
   * Note: Nên sử dụng createTrackingLink() thay thế
   */
  createAffiliateLink(originalUrl: string, campaignId?: string): string {
    // AccessTrade API trả về aff_link có sẵn trong response
    // Function này để tương thích với interface, thực tế sử dụng aff_link từ API
    const encodedUrl = encodeURIComponent(originalUrl)
    return `https://fast.accesstrade.com.vn/deep_link/${campaignId || 'default'}?url=${encodedUrl}`
  }

  /**
   * Lấy chi tiết sản phẩm
   */
  async getProductDetail(params: {
    merchant: string
    product_id: string
  }): Promise<{
    name: string
    desc: string
    short_desc: string
    image: string
    link: string
    brand: string
    category_id: string
    category_name: string
  }> {
    const queryParams = {
      merchant: params.merchant,
      product_id: params.product_id,
    }

    const response = await this.makeRequest<any>('/product_detail', queryParams)
    return response
  }

  /**
   * Lấy danh sách campaigns
   */
  async getCampaigns(params: {
    approval?: 'successful' | 'pending' | 'unregistered'
    campaign_id?: string
    limit?: number
    page?: number
  } = {}): Promise<{
    data: Array<{
      id: string
      name: string
      approval: string
      status: number
      merchant: string
      cookie_duration: number
      description: any
      start_time: string | null
      end_time: string | null
      category: string
      sub_category: string
      type: number
      url: string
      logo: string
      scope: string
    }>
    total?: number
  }> {
    const queryParams = {
      limit: params.limit || 20,
      page: params.page || 1,
      ...(params.approval && { approval: params.approval }),
      ...(params.campaign_id && { campaign_id: params.campaign_id }),
    }

    const response = await this.makeRequest<any>('/campaigns', queryParams)

    if (!response.data || !Array.isArray(response.data)) {
      return { data: [] }
    }

    return {
      data: response.data,
      total: response.total || response.data.length,
    }
  }
}

// Factory function để tạo client instance
export function createAccessTradeClient(apiKey: string): AccessTradeClient {
  return new AccessTradeClient(apiKey)
}

// Default export
export default AccessTradeClient
